# Copilot Instructions for go-example-prompt-ai

## Project Overview
This is a Go REST API project structured for modularity and clarity. It uses [huma](https://github.com/danielgtaylor/huma) for API endpoint registration and request/response modeling. The codebase is organized by versioned API modules and domain-specific handlers.

## Architecture & Structure
- **API Layer**: All HTTP endpoints are defined under `api/v1/` (e.g., `greeting/greeting.go`). Each handler registers its own routes using `huma.Register`.
- **Domain Separation**: Each domain (e.g., greeting) has its own directory and input/output types. Follow this pattern for new features.
- **Versioning**: API versions are nested (`api/v1/`). Future versions should follow the same structure.
- **Request/Response Modeling**: Use Go structs with huma tags for path/query/body fields. Example:
  ```go
  type GreetingInput struct {
      Name string `path:"name" maxLength:"30" example:"world" doc:"Name to greet"`
  }
  ```
- **Output Structs**: Responses are wrapped in a `Body` struct for consistency.

## Developer Workflows
- **Build**: Run `go build ./...` from the project root.
- **Test**: (If tests exist) Run `go test ./...` from the root. Add tests in parallel to new features.
- **Debug**: Use standard Go debugging tools. No custom scripts detected.

## Patterns & Conventions
- **Clean Architecture/DDD**: Prompts in `doc/prompts/` guide AI agents to generate code using Clean Architecture and Domain-Driven Design. Reference these for new CRUD features.
- **Prompt Templates**: Use `doc/prompts/generic-crud-template.md` for new entities. For products, use `products-usecase.md`.
- **Documentation**: API and use case documentation templates are in `doc/guidelines/`.
- **Validation**: Use huma struct tags for validation (e.g., `maxLength`, `example`).
- **Error Handling**: Return errors from handlers as the second return value.

## External Dependencies
- **huma**: Used for API registration and request/response modeling.

## Integration Points
- **API Registration**: All endpoints are registered via `huma.Register` in their respective domain files.
- **No database or external service integration detected** (add instructions if/when added).

## Example: Adding a New Endpoint
1. Create a new directory under `api/v1/` for the domain.
2. Define input/output structs with huma tags.
3. Register the endpoint using `huma.Register`.
4. Add documentation in `doc/prompts/` and `doc/guidelines/` as needed.

## References
- `api/v1/greeting/greeting.go`: Example endpoint implementation
- `doc/prompts/README.md`: AI prompt usage and conventions
- `doc/prompts/generic-crud-template.md`: CRUD template for new entities

---

**Feedback Requested:**
Please review and suggest improvements or clarify any missing project-specific conventions, workflows, or integration details.
