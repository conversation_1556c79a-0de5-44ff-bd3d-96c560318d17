
---

# 🧩 Template: Creating a New Use Case for a Domain (Clean Architecture + DDD)

> ✅ Use this template when creating a new **use case** (e.g., `create`, `update`) for a specific **domain** or **subdomain** (e.g., `products`, `catalog/products`, `billing/invoices`).
> 
> ✳️ It enforces **Domain-Driven Design (DDD)** and **Clean Architecture** principles, supports **modularity**, and is structured for **feature-level reuse**.

---



## 🔧 Template Variables

|Variable|Description|
|---|---|
|`<domain>`|The domain or subdomain (e.g., `products`, `catalog/products`)|
|`<usecase_name>`|The specific use case (e.g., `create`, `update`, `delete`)|

> 📝 **Note**: If the domain has subdomains, reflect that in the folder hierarchy under `usecase/`, e.g., `usecase/catalog/products/create`.



---

## 🏗 Folder Structure


```bash
usecase/
└── <domain>/                          # Domain module (e.g., product, catalog/product)
    ├── register.go                    # 🔧 Registers domain-wide components to DI container
    ├── service/                       # Application-level orchestrator
    │   └── <entity>_service.go
    ├── repository/                    # Repository interface
    │   └── repository.go
    ├── validator/                     # Shared domain custom validation using go-playground/validator
    │   └── alredy_exists.go
    ├── infra/                         # Infrastructure implementation (bun, etc.)
    │   └── bunrepo/                   # Bun-based DB logic
    │       ├── migrations/            # Database migrations for this domain (REQUIRED)
    │       │   └── *.go               # Migration files (prefer .go over .sql format)
    │       ├── entity.go              # DB schema struct (with tags)
    │       └── repository.go          # Bun repository implementation
    └── <usecase_name>/               # e.g., create/, update/
        └── register.go               # 🔧 Registers use case components to DI container
        ├── input.go               # Interactor Input DTO
        ├── mapper.go              # Converts between entity and DTOs (e.g., entity <-> input.go, entity <-> output.go), func name: `ToEntity(input)`, `FromEntity(input)`
        ├── output.go              # Interactor Output DTO
        ├── inport.go              # Use case interface
        ├── interactor.go          # Use case logic (implements inport)
        └── validator.go           # Input validation using go-playground/validator
        ├── viewmodel.go           # Presenter response DTO
        ├── outport.go             # Presenter interface (outport)
        └── presenter.go           # Presenter implementation

```



## 🧠 Guidelines for Implementation

### `interactor.go`

- Implements the `inport` interface (at compile time).
    
- Contains all core business logic.
    
- Validates input, invokes domain rules, and returns the output result to be used by the `outport`.
    

### `inport.go`

- Interface for the use case contract.
    
- Consumed by the service layer.
    

### `validator.go`

- Uses `github.com/go-playground/validator`.
    
- Validates input struct before executing business logic.
    

### `outport.go`

- Defines the contract for result formatting, notification, or side-effects.
    

### `presenter.go`

- Implements the `outport` interface (at compile time).
    
- Typically transforms use case output from the inport into a format suitable for delivery.
    

### `infra/bunrepo/migrations/`

- **REQUIRED**: All database migrations related to this domain/use case must be placed here.

- **Format preference**: Use `.go` migration files over `.sql` files when possible.

- **Naming convention**: Follow Bun's migration naming patterns (e.g., `20240101120000_create_products_table.go`).

- **Scope**: Migrations should be domain-specific and self-contained within this folder.

### `service/<domain>_service.go`

- Orchestrates use case execution (inport/outport) interactions.

- Connects use cases to controller-level entry points.

**Service example:**

```go
type ProductService struct {
	// ...
}


func (s *ProductService) CreateProduct(input create.CreateProductInput) (create.CreateProductViewModel, error) {
	// Execute the use case through the inport
	inOutput, err := s.createProductInport.Execute(input)
	
	// Present the result through the outport
	return s.createProductOutport.Present(inOutput, err)
}
```

### `register.go`

- Required in both `<domain>/register.go`, `<domain>/<subpackage>/register.go`, `<domain>/<usecase>/register.go`, and so on for subpackages.
    
- Registers dependencies into the [`do` dependency injection container](https://github.com/samber/do).

**Example:**

```go
func Register(injector *do.Injector) error {
	do.Provide(injector, RegisterCreateProductInport)
    // ...
	return nil
}

func RegisterCreateProductInport(i *do.Injector) (CreateProductInport, error) {
    return &CreateProductInteractor(
        // ...
    ), nil
}

```

---

## 🏷 Naming Conventions

| Item                 | Convention              |
| -------------------- | ----------------------- |
| Repository interface | `<Entity>Repository`    |
| Validator struct     | `<Entity>Validator`     |
| inport/outport interfaces| `<Verb><Entity>Inport`, `<Verb><Entity>Outport` |
| ViewModel struct     | `<Verb><Entity>ViewModel`      |



---

## 🧭 Why This Structure?

- ✅ **Feature modularity**: Each use case is self-contained and easy to copy or reuse.
    
- ✅ **Clean boundaries**: No cross-contamination between domain, application, and infrastructure.
    
- ✅ **High testability**: Each component (interactor, validator, presenter) is easily unit tested.
    
- ✅ **Future-proof**: Supports multiple delivery mechanisms (HTTP, gRPC, CLI) via the service layer.
    
- ✅ **Portability**: The entire `<domain>` folder can be moved across projects with minimal friction — including subdomains.
    

---

## 📝 Final Notes

> ✅ **When creating a new use case:**
> 
> 1. **Specify the `<domain>` and `<usecase_name>`** — subdomains should be reflected in the path.
>     
> 2. **Check existing use cases** in the project to follow the same structure and naming patterns.
>     
> 3. **Use this template** to ensure consistency, scalability, and architectural clarity.
>     

---