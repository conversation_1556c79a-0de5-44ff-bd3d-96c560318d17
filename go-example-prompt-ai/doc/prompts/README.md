# 🤖 AI Agent Prompts for CRUD Generation

This directory contains optimized prompts for AI agents to generate complete CRUD systems following Clean Architecture and DDD principles.

## 📁 Available Prompts

### 1. `products-usecase.md` - Complete Products CRUD
**Use Case**: Generate a full product catalog management system
**Features**: 
- Product creation with metadata, tags, images, attributes
- Product updates with partial data support
- Product deletion with existence validation
- Single product retrieval
- Product listing with pagination
- Complete validation and error handling

### 2. `generic-crud-template.md` - Universal CRUD Template
**Use Case**: Generate CRUD for any domain/entity
**Features**:
- Placeholder-based template for any entity type
- Complete architectural structure
- Customizable data models
- Reusable for any domain (users, orders, invoices, etc.)

## 🚀 How to Use

### For Augment Code Agents

1. **Copy the entire prompt** from the relevant `.md` file
2. **Customize if needed** (for generic template, replace placeholders)
3. **Paste into Augment Code** with instruction: "Follow this prompt exactly"
4. **Review generated code** against the checklist in the prompt

### For Other AI Agents

1. **Ensure the agent has access** to the `guidelines/usecase-template.md` file
2. **Provide the complete prompt** including all sections
3. **Emphasize following the template exactly**
4. **Request complete implementation** of all CRUD operations

## ✅ Success Criteria

A successful implementation should include:
- [ ] All 5 CRUD operations (Create, Read, Update, Delete, List)
- [ ] Complete Clean Architecture structure
- [ ] Domain and input validation
- [ ] Error handling with meaningful messages
- [ ] Database integration with Bun ORM
- [ ] Proper pagination for list operations
- [ ] Modular, testable code structure

## 🔧 Customization Guide

### For New Domains

1. **Start with `generic-crud-template.md`**
2. **Replace placeholders**:
   - `{DOMAIN_PATH}` → your domain path (e.g., `user/profiles`)
   - `{ENTITY_NAME}` → your entity name (e.g., `User`)
   - `{ENTITY_NAME_LOWER}` → lowercase entity (e.g., `user`)
   - `{MODULE_NAME}` → your Go module name
3. **Define your data model** with specific fields and validation rules
4. **Customize business logic** for your domain requirements

### Example Customizations

**For User Management**:
```
{DOMAIN_PATH} → user/profiles
{ENTITY_NAME} → User  
{ENTITY_NAME_LOWER} → user
```

**For Order Management**:
```
{DOMAIN_PATH} → sales/orders
{ENTITY_NAME} → Order
{ENTITY_NAME_LOWER} → order
```

## 🎯 Best Practices

1. **Always reference the template**: Ensure agents read `guidelines/usecase-template.md` first
2. **Be specific about requirements**: Include exact field names, validation rules, and business logic
3. **Request complete implementation**: Don't allow partial implementations
4. **Verify architecture compliance**: Check that generated code follows Clean Architecture
5. **Test the generated code**: Ensure all CRUD operations work as expected

## 🚨 Common Issues & Solutions

**Issue**: Agent skips some files or components
**Solution**: Emphasize the deliverables checklist and request all files

**Issue**: Generated code doesn't follow naming conventions
**Solution**: Highlight the naming conventions section in the prompt

**Issue**: Missing validation or error handling
**Solution**: Stress the validation coverage and error handling requirements

**Issue**: Database integration issues
**Solution**: Ensure Bun ORM dependencies are available and properly configured

## 📚 Related Documentation

- `../guidelines/usecase-template.md` - Architectural template and conventions
- `../../go.mod` - Project dependencies and module configuration
- Generated code examples in `../../usecase/catalog/products/` directory
