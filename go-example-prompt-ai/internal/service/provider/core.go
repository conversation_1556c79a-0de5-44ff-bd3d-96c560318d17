package provider

import (
	"context"

	"demo/core/bootstrap"

	"github.com/samber/do"
)

func NewCoreServiceProvider(i *do.Injector) *CoreServiceProvider {
	return &CoreServiceProvider{
		injector: i,
	}
}

type CoreServiceProvider struct {
	injector *do.Injector
}

func (s *CoreServiceProvider) Register(ctx context.Context, container bootstrap.Container) error {
	return nil
}

func (s *CoreServiceProvider) Boot(ctx context.Context, container bootstrap.Container) error {
	return nil
}

func (s *CoreServiceProvider) Teardown(ctx context.Context, container bootstrap.Container) error {
	return nil
}
