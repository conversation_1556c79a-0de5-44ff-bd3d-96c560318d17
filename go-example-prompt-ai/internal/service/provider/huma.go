package provider

import (
	"context"
	"fmt"
	"net/http"
	"sync"

	"demo/api"
	"demo/core/bootstrap"

	"github.com/danielgtaylor/huma/v2"
	"github.com/danielgtaylor/huma/v2/adapters/humago"
	"github.com/danielgtaylor/huma/v2/humacli"
	"github.com/samber/do"
)

// HumaOptions for the CLI.
type HumaOptions struct {
	Port int `help:"Port to listen on" short:"p" default:"8888"`
}

type HumaServiceProvider struct {
	injector *do.Injector
	wg       sync.WaitGroup
	mux      *http.ServeMux
	cli      humacli.CLI
}

func NewHumaServiceProvider(i *do.Injector) *HumaServiceProvider {
	mux := http.NewServeMux()
	return &HumaServiceProvider{
		injector: i,
		mux:      mux,
	}
}

func (s *HumaServiceProvider) Register(ctx context.Context, container bootstrap.Container) error {
	cli := humacli.New(func(hooks humacli.Hooks, options *HumaOptions) {
		// Create a new mux & API using humago

		humaAPI := humago.NewWithPrefix(s.mux, "/api", huma.DefaultConfig("My API", "1.0.0"))
		api.Register(humaAPI)

		// Tell the CLI how to start your server.
		hooks.OnStart(func() {
			http.ListenAndServe(fmt.Sprintf(":%d", options.Port), s.mux)
		})
	})
	s.cli = cli
	return nil
}

func (s *HumaServiceProvider) Boot(ctx context.Context, container bootstrap.Container) error {
	// Create a CLI app which takes a port option.

	// Run the CLI. When passed no commands, it starts the server.
	s.cli.Run()
	return nil
}

func (s *HumaServiceProvider) Teardown(ctx context.Context, container bootstrap.Container) error {
	return nil
}
