package main

import (
	"context"

	"demo/core/bootstrap"
	"demo/internal/service/provider"

	"github.com/samber/do"

	_ "github.com/danielgtaylor/huma/v2/formats/cbor"
)

func main() {
	ctx := context.Background()
	injector := do.New()
	bootstrapper := bootstrap.NewDefaultBootStrapper(
		injector,
		provider.NewCoreServiceProvider(injector),
	)
	if err := bootstrapper.Boot(ctx); err != nil {
		panic(err)
	}
	defer bootstrapper.Shutdown(ctx)
}
